package nlp

import (
	"log"
	"strings"
	"sync"
	"time"
)

// IntegratedProcessor 集成的NLP处理器，结合jieba和spago
type IntegratedProcessor struct {
	jiebaProcessor *JiebaProcessor
	spagoProcessor *SpagoProcessor
	initialized    bool
	mutex          sync.RWMutex
}

// IntegratedResult 集成处理结果
type IntegratedResult struct {
	// 基础分词结果
	Tokens    []string `json:"tokens"`
	Keywords  []string `json:"keywords"`
	Entities  []Entity `json:"entities"`
	Topics    []Topic  `json:"topics"`
	Sentiment string   `json:"sentiment"`

	// Spago高级结果
	SpagoResult *SpagoResult `json:"spago_result,omitempty"`

	// 元数据
	ProcessingTime time.Duration `json:"processing_time"`
	Confidence     float64       `json:"confidence"`
	Method         string        `json:"method"` // "jieba", "spago", "integrated"
}

// NewIntegratedProcessor 创建集成处理器
func NewIntegratedProcessor() *IntegratedProcessor {
	log.Printf("🚀 初始化集成NLP处理器 (Jieba + Spago)")

	processor := &IntegratedProcessor{
		initialized: false,
	}

	// 异步初始化
	go processor.initializeAsync()

	return processor
}

// initializeAsync 异步初始化
func (ip *IntegratedProcessor) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 集成处理器初始化失败: %v", r)
			ip.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化集成处理器组件...")

	// 1. 延迟初始化jieba处理器（避免启动时崩溃）
	ip.jiebaProcessor = nil
	log.Printf("   ⚠️ Jieba处理器延迟初始化（避免启动崩溃）")

	// 2. 初始化spago处理器
	ip.spagoProcessor = NewSpagoProcessor()
	log.Printf("   ✅ Spago处理器初始化完成")

	// 等待spago完全初始化
	time.Sleep(time.Second * 2)

	ip.mutex.Lock()
	ip.initialized = true
	ip.mutex.Unlock()

	log.Printf("✅ 集成NLP处理器初始化完成")
}

// ProcessText 处理文本
func (ip *IntegratedProcessor) ProcessText(text string) *IntegratedResult {
	startTime := time.Now()

	ip.mutex.RLock()
	isInitialized := ip.initialized
	ip.mutex.RUnlock()

	if !isInitialized {
		log.Printf("⚠️ 集成处理器未完全初始化，使用基础处理")
		return ip.basicProcess(text)
	}

	log.Printf("🧠 集成NLP处理: %s", text[:min(len(text), 50)])

	result := &IntegratedResult{
		ProcessingTime: time.Since(startTime),
	}

	// 1. 使用jieba进行基础中文处理
	if ip.jiebaProcessor != nil {
		result.Tokens = ip.jiebaProcessor.SegmentText(text)

		keywords := ip.jiebaProcessor.ExtractKeywords(text, 10)
		result.Keywords = ip.convertKeywordsToStrings(keywords)

		result.Entities = ip.jiebaProcessor.ExtractEntities(text)
		result.Topics = ip.jiebaProcessor.ExtractTopics(text, 5)

		sentiment, _ := ip.jiebaProcessor.AnalyzeSentiment(text)
		result.Sentiment = sentiment

		result.Method = "jieba"
		log.Printf("   ✅ Jieba处理完成: %d tokens, %d keywords", len(result.Tokens), len(result.Keywords))
	}

	// 2. 使用spago进行高级处理
	if ip.spagoProcessor != nil {
		spagoResult := ip.spagoProcessor.ProcessText(text)
		result.SpagoResult = spagoResult

		// 如果jieba没有处理，使用spago的结果
		if result.Tokens == nil || len(result.Tokens) == 0 {
			result.Tokens = spagoResult.Tokens
		}

		// 融合置信度
		if result.Method == "jieba" {
			result.Method = "integrated"
			result.Confidence = (0.6 + spagoResult.Confidence*0.4) // jieba权重0.6，spago权重0.4
		} else {
			result.Method = "spago"
			result.Confidence = spagoResult.Confidence
		}

		log.Printf("   ✅ Spago处理完成: 置信度=%.3f", spagoResult.Confidence)
	}

	// 3. 如果都没有处理成功，使用基础处理
	if result.Tokens == nil || len(result.Tokens) == 0 {
		return ip.basicProcess(text)
	}

	result.ProcessingTime = time.Since(startTime)
	log.Printf("✅ 集成处理完成: 方法=%s, 置信度=%.3f, 耗时=%v",
		result.Method, result.Confidence, result.ProcessingTime)

	return result
}

// basicProcess 基础处理
func (ip *IntegratedProcessor) basicProcess(text string) *IntegratedResult {
	return &IntegratedResult{
		Tokens:         strings.Fields(text),
		Keywords:       []string{},
		Entities:       []Entity{},
		Topics:         []Topic{},
		Sentiment:      "中性",
		SpagoResult:    nil,
		ProcessingTime: time.Millisecond * 5,
		Confidence:     0.3,
		Method:         "basic",
	}
}

// convertKeywordsToStrings 转换关键词为字符串数组
func (ip *IntegratedProcessor) convertKeywordsToStrings(keywords []WordInfo) []string {
	result := make([]string, len(keywords))
	for i, kw := range keywords {
		result[i] = kw.Word
	}
	return result
}

// Close 关闭处理器
func (ip *IntegratedProcessor) Close() {
	if ip.jiebaProcessor != nil {
		ip.jiebaProcessor.Close()
	}
	if ip.spagoProcessor != nil {
		ip.spagoProcessor.Close()
	}
}

// IsInitialized 检查是否已初始化
func (ip *IntegratedProcessor) IsInitialized() bool {
	ip.mutex.RLock()
	defer ip.mutex.RUnlock()
	return ip.initialized
}

// GetProcessorStatus 获取处理器状态
func (ip *IntegratedProcessor) GetProcessorStatus() map[string]interface{} {
	status := map[string]interface{}{
		"initialized":     ip.IsInitialized(),
		"jieba_available": ip.jiebaProcessor != nil && ip.jiebaProcessor.jieba != nil,
		"spago_available": ip.spagoProcessor != nil,
	}

	if ip.spagoProcessor != nil {
		ip.spagoProcessor.mutex.RLock()
		status["spago_initialized"] = ip.spagoProcessor.initialized
		ip.spagoProcessor.mutex.RUnlock()
	}

	return status
}
